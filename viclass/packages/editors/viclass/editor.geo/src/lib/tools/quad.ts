/**
 * Quadilateral related tools
 */

import { point, vector } from '@flatten-js/core';
import { UIPointerEventData } from '@viclass/editor.core';
import { syncRemovePreviewCmd } from '../cmd';
import { GeoEpsilon, GeometryEditor, GeoPointerEvent, GeoPointerNotInError, RenderVertex } from '../geo.api';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, QuadToolState, RenderLineSegment } from '../model';
import { GeoKeyboardEvent, GeometryToolType } from '../model/geo.models';
import { pLine, PreviewQueue, pVertex } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import {
    circleCheck,
    circleTransform,
    ElementSelector,
    halfCircleCheck,
    halfCircleTransform,
    nothing,
    or,
    OrSelector,
    perpBisectorCheck,
    perpBisectorTransform,
    perpLinesCheck,
    perpLinesTransform,
    SelectedVertex,
    then,
    ThenSelector,
    triangleWithProj,
    vert,
    vertex,
} from '../selectors';
import { isocelesTriangleBaseNApex, rightTriangleBaseNApex } from './create.triangle.tool';
import { GeometryTool } from './geo.tool';
import { buildPreviewVertexRenderProp, getFocusDocCtrl } from './tool.utils';

abstract class ParallelQuad extends GeometryTool<QuadToolState> {
    declare selLogic: ThenSelector;
    protected first3Points: ElementSelector<SelectedVertex[]>;
    protected lastPoint: OrSelector<[SelectedVertex | 'nothing']>;
    pQ = new PreviewQueue();
    numOptions = 2;
    diagonalMode = 1;
    baseOrdering: (rv: RenderVertex[], isDiagonal: boolean) => [number, number, number];

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
        this.registerKeyboardHandling({
            event: 'keyup',
            keys: ['shift'],
            global: false,
        });
    }

    abstract createSelLogic();

    override resetState() {
        this.selLogic.reset();
        super.resetState();
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        console.log(this.numOptions);
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.drawMode = ++this.toolState.drawMode % this.numOptions;
            this.toolbar.update(this.toolType, this.toolState);
        }

        return event;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event => this.doTrySelection(event, ctrl));
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    p4Pos(rv: RenderVertex[], isDiagonal: boolean): { apex: number; b1: number; b2: number; p4Coords: number[] } {
        const [apex, b1, b2] = this.baseOrdering(rv, isDiagonal);

        const c = point((rv[b1].coords[0] + rv[b2].coords[0]) / 2, (rv[b1].coords[1] + rv[b2].coords[1]) / 2);
        const x = c.translate(vector(c.x - rv[apex].coords[0], c.y - rv[apex].coords[1]));
        const p4 = pVertex(-21, [x.x, x.y, 0]);
        return { apex, b1, b2, p4Coords: p4.coords };
    }

    checkLastPoint(el: RenderVertex, doc: GeoDocCtrl): boolean {
        const first3Points = this.selLogic.selected[0] as SelectedVertex[];
        const { p4Coords } = this.p4Pos(
            first3Points.map(v => vert(v)),
            this.toolState.drawMode == this.diagonalMode
        );

        const p4 = point(p4Coords[0], p4Coords[1]);
        const pE = point(el.coords[0], el.coords[1]);

        return p4.distanceTo(pE)[0] < GeoEpsilon;
    }

    lastPointSelector(): OrSelector<[SelectedVertex | 'nothing']> {
        return or([
            vertex({
                genPreview: false,
                cursor: this.pointerHandler.cursor,
                cfunc: this.checkLastPoint.bind(this),
                refinedFilter: (el: RenderVertex) => {
                    const first3Points = this.selLogic.selected[0] as SelectedVertex[];
                    if (first3Points.map(v => vert(v).relIndex).includes(el.relIndex)) return false;

                    return true;
                },
            }),
            nothing(),
        ]);
    }

    doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        try {
            const selected = this.selLogic.trySelect(event, ctrl);
            if (selected) {
                const first3Points = selected[0] as SelectedVertex[];
                const rv = first3Points.map(v => vert(v));

                if (first3Points.length >= 2) {
                    this.pQ.add(
                        pLine(
                            ctrl,
                            -20,
                            RenderLineSegment,
                            vert(selected[0][0] as SelectedVertex),
                            vert(selected[0][1] as SelectedVertex)
                        )
                    );
                }
                const ts = this.toolState;
                if (first3Points.length == 3) {
                    if (rv[2].relIndex < 0) {
                        rv[2].renderProp = buildPreviewVertexRenderProp();
                        rv[2].renderProp.pointColor = '#ff0000';
                    }

                    const { apex, b1, b2, p4Coords } = this.p4Pos(rv, ts.drawMode == this.diagonalMode);

                    this.pQ.add(pLine(ctrl, -51, RenderLineSegment, rv[apex], rv[b1]));
                    this.pQ.add(pLine(ctrl, -52, RenderLineSegment, rv[apex], rv[b2]));

                    this.pQ.add(pLine(ctrl, -53, RenderLineSegment, p4Coords, rv[b1])); // we don't use p4 directly, but only its coords
                    this.pQ.add(pLine(ctrl, -54, RenderLineSegment, p4Coords, rv[b2]));

                    if (ctrl.rendererCtrl.previewElAt(-20)) syncRemovePreviewCmd(-20, ctrl);
                }
            }
            this.pQ.flush(ctrl);
        } catch (e) {
            console.log('Some error occur', e);
        }
    }
}

export class CreateRectangleTool extends ParallelQuad {
    readonly toolType: GeometryToolType = 'CreateRectangleTool';
    override baseOrdering = rightTriangleBaseNApex;

    createSelLogic() {
        this.first3Points = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) => (this.toolState.drawMode == 0 ? perpLinesTransform(f2p, el) : halfCircleTransform(f2p, el)),
            (f2p, el) => (this.toolState.drawMode == 0 ? perpLinesCheck(f2p, el) : halfCircleCheck(f2p, el)),
            {
                flatten: 1, // unbox the then of the first 3 points
            }
        ) as unknown as ElementSelector<SelectedVertex[]>;
        this.selLogic = then([this.first3Points, this.lastPointSelector()], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {}

    private buildRectangleFromLineSegmentAndLengthConstruction(
        rectangleName: string,
        lineName: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'LineSegmentAndLength');
        construction.name = rectangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-LengthValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildRectangleFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateParallelogramTool extends ParallelQuad {
    readonly toolType: GeometryToolType = 'CreateParallelogramTool';
    override numOptions = 3;
    override diagonalMode = 2;

    override baseOrdering = (): [number, number, number] => {
        const ts = this.toolState;
        if (ts.drawMode == 0) return [1, 0, 2];
        else if (ts.drawMode == 1) return [0, 1, 2];
        else if (ts.drawMode == 2) return [2, 0, 1];
        else return [1, 0, 2];
    };

    createSelLogic() {
        this.first3Points = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) => el,
            () => true,
            {
                flatten: 1, // unbox the then of the first 3 points
            }
        ) as unknown as ElementSelector<SelectedVertex[]>;
        this.selLogic = then([this.first3Points, this.lastPointSelector()], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {}

    private buildParallelogramConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Parallelogram/ParallelogramEC',
            'Parallelogram',
            'ByPointsName'
        );
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }

    private buildParallelogramFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Parallelogram/ParallelogramEC',
            'Parallelogram',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateRhombusTool extends ParallelQuad {
    readonly toolType: GeometryToolType = 'CreateRhombusTool';
    override numOptions = 3;
    override diagonalMode = 2;
    override baseOrdering = isocelesTriangleBaseNApex;

    createSelLogic() {
        this.first3Points = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) =>
                this.toolState.drawMode == this.diagonalMode
                    ? perpBisectorTransform(f2p, el)
                    : this.toolState.drawMode == 0
                      ? circleTransform(f2p, el, 0)
                      : circleTransform(f2p, el, 1),
            (f2p, el) =>
                this.toolState.drawMode == this.diagonalMode ? perpBisectorCheck(f2p, el) : circleCheck(f2p, el),
            {
                flatten: 1, // unbox the then of the first 3 points
            }
        ) as unknown as ElementSelector<SelectedVertex[]>;
        this.selLogic = then([this.first3Points, this.lastPointSelector()], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {}

    private buildRectangleFromLineSegmentAndLengthConstruction(
        rectangleName: string,
        lineName: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'LineSegmentAndLength');
        construction.name = rectangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-LengthValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildRectangleFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}
