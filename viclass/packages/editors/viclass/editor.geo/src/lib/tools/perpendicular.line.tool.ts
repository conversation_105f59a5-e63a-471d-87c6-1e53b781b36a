import { buildDocumentAwarenessCmdOption } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderLineSegment, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vertex, vertexOnStroke, vert } from '../selectors';
import { GeometryTool } from './geo.tool';
import { addHistoryItemFromConstructionResponse, getFocusDocCtrl, handleIfPointerNotInError } from './tool.utils';

/**
 * Perpendicular Line Tool - Creates perpendicular lines using selector pattern
 * <AUTHOR>
 */
export class CreatePerpendicularLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePerpendicularLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedVertices: RenderVertex[] = [];
    previewLine: RenderLine | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedVertices = [];
        this.previewLine = undefined;
        super.resetState();
    }

    /**
     * Creates the selection logic: select line first, then 1 or 2 vertices
     */
    private createSelLogic() {
        // First select a line
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Create vertex selector for both regular vertices and vertices on strokes
        const vertexSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    preview: true, // Enable preview element selection
                    syncPreview: true,
                }),
            ],
            { flatten: true }
        );

        // Create two different vertex selection patterns
        const oneVertexSelector = then([vertexSelector], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 1) return;

                this.selectedVertices = [vert(selected[0] as any)];
                await this.performConstructionWithVertices(doc);
            },
        });

        const twoVertexSelector = then([vertexSelector, vertexSelector], {
            onPartialSelection: (currentSelected: any, allSelected: any[], selector: ThenSelector, doc: GeoDocCtrl) => {
                if (allSelected && allSelected.length === 1) {
                    // After selecting first vertex, store it and continue
                    this.selectedVertices = [vert(allSelected[0] as any)];
                    return true; // Continue selection
                }
                return true;
            },
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 2) return;

                this.selectedVertices = [vert(selected[0] as any), vert(selected[1] as any)];
                await this.performConstructionWithVertices(doc);
            },
        });

        // Main selection logic: select line, then either 1 or 2 vertices
        const vertexSelectionOptions = or([oneVertexSelector, twoVertexSelector], { flatten: false });

        this.selLogic = then([lineSelector, vertexSelectionOptions], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [line, vertexSelection] = selector.selected;
                this.selectedLine = line as RenderLine;
                // The vertex selection completion is handled by individual vertex selectors
            },
        });
    }

    /**
     * Shows preview of the perpendicular line after selecting line and vertices
     */
    private showPreviewLineWithVertices(ctrl: GeoDocCtrl) {
        if (!this.selectedLine || this.selectedVertices.length === 0) return;

        // Calculate perpendicular line vector (rotate original line vector by 90 degrees)
        const lineVector = this.selectedLine.vector;
        const perpendicularVector = [-lineVector[1], lineVector[0]];

        if (this.selectedVertices.length === 1) {
            // Show preview line through the selected vertex
            this.previewLine = pLine(
                ctrl,
                -31, // Preview ID for perpendicular line
                RenderLine,
                this.selectedVertices[0],
                undefined,
                perpendicularVector
            );
        } else if (this.selectedVertices.length === 2) {
            // Show preview line segment between the two vertices
            this.previewLine = pLine(
                ctrl,
                -31, // Preview ID for perpendicular line segment
                RenderLineSegment,
                this.selectedVertices[0],
                this.selectedVertices[1]
            );
        }

        if (this.previewLine) {
            this.pQ.add(this.previewLine);
        }
    }

    /**
     * Shows preview perpendicular line immediately after selecting a line, before selecting any vertices
     * This creates a preview line that will be updated as the user hovers over potential vertices
     */
    private showPreviewLineAfterLineSelection(ctrl: GeoDocCtrl) {
        if (!this.selectedLine) return;

        // Calculate perpendicular line vector (rotate original line vector by 90 degrees)
        const lineVector = this.selectedLine.vector;
        const perpendicularVector = [-lineVector[1], lineVector[0]];

        // Get start and end coordinates of the selected line
        const startCoords = this.selectedLine.coord('start', ctrl.rendererCtrl);
        const endCoords = this.selectedLine.coord('end', ctrl.rendererCtrl);

        if (!startCoords || !endCoords) return;

        // Create a preview line at the center of the selected line as a starting point
        // This will be updated when the user hovers over vertices
        const lineMidpoint = {
            coords: [
                (startCoords[0] + endCoords[0]) / 2,
                (startCoords[1] + endCoords[1]) / 2,
                0, // Z coordinate
            ],
            name: 'temp_midpoint',
        } as RenderVertex;

        this.previewLine = pLine(
            ctrl,
            -31, // Preview ID for perpendicular line
            RenderLine,
            lineMidpoint,
            undefined,
            perpendicularVector
        );

        if (this.previewLine) {
            this.pQ.add(this.previewLine);
        }
    }

    /**
     * Updates the preview line to go through a different vertex while keeping it perpendicular
     */
    private updatePreviewLineToVertex(ctrl: GeoDocCtrl, trialVertex: any) {
        if (!this.selectedLine || !trialVertex) return;

        // Extract the actual RenderVertex from the trial selection
        const vertex = vert(trialVertex);
        if (!vertex) return;

        // Calculate perpendicular line vector (rotate original line vector by 90 degrees)
        const lineVector = this.selectedLine.vector;
        const perpendicularVector = [-lineVector[1], lineVector[0]];

        // Create new preview line through the hovered vertex with perpendicular vector
        // Using the same ID will replace the existing preview line
        this.previewLine = pLine(
            ctrl,
            -31, // Preview ID for perpendicular line (same ID replaces existing)
            RenderLine,
            vertex,
            undefined,
            perpendicularVector
        );

        this.pQ.add(this.previewLine);
    }

    /**
     * Updates the preview line segment to go through a different second vertex
     */
    private updatePreviewLineSegmentToVertex(ctrl: GeoDocCtrl, trialVertex: any) {
        if (!this.selectedLine || this.selectedVertices.length === 0 || !trialVertex) return;

        // Extract the actual RenderVertex from the trial selection
        const vertex = vert(trialVertex);
        if (!vertex) return;

        // Create new preview line segment between first selected vertex and hovered vertex
        // Using the same ID will replace the existing preview line
        this.previewLine = pLine(
            ctrl,
            -31, // Preview ID for perpendicular line segment (same ID replaces existing)
            RenderLineSegment,
            this.selectedVertices[0],
            vertex
        );

        this.pQ.add(this.previewLine);
    }

    /**
     * Performs construction based on selected line and vertices
     */
    private async performConstructionWithVertices(ctrl: GeoDocCtrl) {
        if (!this.selectedLine || this.selectedVertices.length === 0) return;

        if (this.selectedVertices.length === 1) {
            // Single vertex: create perpendicular line through the vertex
            await this.buildPerpendicularLine(ctrl, this.selectedVertices[0]);
        } else if (this.selectedVertices.length === 2) {
            // Two vertices: create perpendicular line segment between the vertices
            await this.buildPerpendicularLineSegment(ctrl, this.selectedVertices[0], this.selectedVertices[1]);
        }
    }

    /**
     * Builds a perpendicular line through a single vertex
     */
    private async buildPerpendicularLine(ctrl: GeoDocCtrl, vertex: RenderVertex) {
        const construction = this.buildLine('', this.selectedLine.name, this.selectedLine.elType, vertex.name);
        await this.executeConstruction(ctrl, construction);
    }

    /**
     * Builds a perpendicular line segment between two vertices
     */
    private async buildPerpendicularLineSegment(ctrl: GeoDocCtrl, vertex1: RenderVertex, vertex2: RenderVertex) {
        // Calculate the distance/ratio for the line segment
        const startPoint = vertex1.coords;
        const endPoint = vertex2.coords;
        const lineVector = this.selectedLine.vector;
        const perpendicularVector = [-lineVector[1], lineVector[0]];

        // Calculate unit vector and scaling factor
        const vectorLength = Math.sqrt(perpendicularVector[0] ** 2 + perpendicularVector[1] ** 2);
        const unitVector = [perpendicularVector[0] / vectorLength, perpendicularVector[1] / vectorLength];

        const segmentVector = [endPoint[0] - startPoint[0], endPoint[1] - startPoint[1]];
        const segmentLength = Math.sqrt(segmentVector[0] ** 2 + segmentVector[1] ** 2);

        // Determine direction
        const dotProduct = unitVector[0] * segmentVector[0] + unitVector[1] * segmentVector[1];
        const k = dotProduct >= 0 ? segmentLength : -segmentLength;

        const construction = this.buildLineSegment(
            `${vertex1.name}${vertex2.name}`,
            this.selectedLine.name,
            this.selectedLine.elType,
            vertex1.name,
            k
        );
        await this.executeConstruction(ctrl, construction);
    }

    /**
     * Executes the construction request
     */
    private async executeConstruction(ctrl: GeoDocCtrl, construction: GeoElConstructionRequest) {
        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await this.editor.geoGateway.construct(ctrl.state.globalId, [
                    { construction },
                ]);

                await syncRenderCommands(constructResponse.render, ctrl);
                addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                this.resetState();
            }
        );
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: any, ctrl: GeoDocCtrl) {
        // Continue with selection logic
        const selected = this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (selected && selected.length === 1) {
            // Line selected, show initial preview
            this.selectedLine = selected[0] as RenderLine;
            this.showPreviewLineAfterLineSelection(ctrl);
        } else if (selected && selected.length === 2) {
            // Line and vertex selection in progress, show updated preview
            this.selectedLine = selected[0] as RenderLine;
            const vertexSelection = selected[1];

            // Handle preview for vertex selection
            if (Array.isArray(vertexSelection) && vertexSelection.length > 0) {
                this.selectedVertices = vertexSelection.map((v: any) => vert(v));
                this.showPreviewLineWithVertices(ctrl);
            }
        }

        // Handle hover preview updates
        if (this.selectedLine && selected && selected.length === 1) {
            // We have selected line but no vertices yet, try to update preview for single vertex
            this.tryUpdatePreviewForSingleVertex(event, ctrl);
        } else if (this.selectedLine && this.selectedVertices.length === 1) {
            // We have selected line and first vertex, try to update preview for second vertex
            this.tryUpdatePreviewForSecondVertex(event, ctrl);
        }

        // Always flush at the end - this is the pattern used by other tools
        this.pQ.flush(ctrl);
    }

    /**
     * Try to update preview for single vertex selection (perpendicular line through vertex)
     */
    private tryUpdatePreviewForSingleVertex(event: any, ctrl: GeoDocCtrl) {
        // Create a temporary vertex selector to get trial vertex
        const tempVertexSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    preview: true,
                    syncPreview: true,
                }),
            ],
            { flatten: true }
        );

        const trialVertex = tempVertexSelector?.tryPreview?.(event, ctrl);
        if (trialVertex) {
            this.updatePreviewLineToVertex(ctrl, trialVertex);
        }
    }

    /**
     * Try to update preview for second vertex selection (perpendicular line segment)
     */
    private tryUpdatePreviewForSecondVertex(event: any, ctrl: GeoDocCtrl) {
        // Create a temporary vertex selector to get trial vertex for second vertex
        const tempVertexSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    preview: true,
                    syncPreview: true,
                }),
            ],
            { flatten: true }
        );

        const trialVertex = tempVertexSelector?.tryPreview?.(event, ctrl);
        if (trialVertex) {
            this.updatePreviewLineSegmentToVertex(ctrl, trialVertex);
        }
    }

    private buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'ThoughPointPerpendicularWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'PerpendicularWithNewPoint'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }
}
