﻿import {
    CachingReflowSync,
    CriticalDocumentAPIErr,
    Cursor,
    DocumentId,
    KeyboardEventListener,
    KeyboardHandlingItem,
    LocatableEvent,
    MouseEventData,
    MouseEventListener,
    MouseHandlingItem,
    mouseLocation,
    NativeEventTarget,
    NonCriticalDocumentAPIErr,
    pointerCursor,
    PointerEventData,
    PointerEventListener,
    PointerHandlingItem,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
    Position,
    Tool,
    ToolBar,
    ToolState,
    UserInputHandlerType,
    ViewportManager,
} from '@viclass/editor.core';
import { AxiosError } from 'axios';
import { BehaviorSubject } from 'rxjs';
import { syncEndPreviewModeCommand } from '../cmd';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    ApplyConstructionResponse,
    GeoRelType,
    GeoRenderElement,
    RenderNamePatternModel,
    ValidationResult,
} from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent, GeoToolEventData } from '../model/geo.models';
import { GeoDocCtrl, GeoSelectHitContext } from '../objects';
import { GeoRenderer } from '../renderer';
import { MultiElementSelector } from '../selectors/selection.dsl';
import { ElementSelector, SelectableType } from '../selectors/selectors';
import {
    calculatePosInLayer,
    convertPointBySnapTool,
    defaultNonUIPointerEventHandler,
    geoDocReg,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    validatePointerPos,
} from './tool.utils';

/**
 * Validates a point name based on type-specific rules and existing names.
 *
 * @param {GeoDocCtrl} ctrl - The document controller containing rendererCtrl.
 * @param {string} name - The name to validate.
 * @param {string[]} inputPointNames - Array of already entered point names.
 * @param {string[]} [namesToAvoid=[]] - Names to avoid during validation.
 * @param {number} [idx=0] - Index of the point being validated.
 * @param {GeoRelType} type - The geographic relation type.
 * @param {(idx: number, name: string, type: GeoRelType) => ValidationResult} [onValidate] - Optional callback for additional validation.
 * @returns {ValidationResult} The result of the name validation.
 */

export function validateName(
    ctrl: GeoDocCtrl,
    name: string,
    inputPointNames: string[],
    namesToAvoid: string[] = [],
    idx: number = 0,
    type: GeoRelType,
    onValidate?: (idx: number, name: string, type: GeoRelType) => ValidationResult
): ValidationResult {
    // Trim whitespace from the name
    name = name?.trim();

    // Return invalid if name is empty or undefined
    if (!name) return { valid: false };

    // Check if the name matches the regex pattern for the given type
    const valid = RenderNamePatternModel[type].regex.some(rgx => rgx.test(name));
    if (valid) {
        // Check for duplicate names in inputPointNames
        if (inputPointNames.filter(n => n === name).length > 1) {
            return { valid: false, message: `Tên ${name} nhập trùng` };
        }

        // Determine if the type is RenderVertex
        const isVertex = type === 'RenderVertex';
        // Filter elements by type (RenderVertex or non-RenderVertex) and extract names
        const elements = ctrl.rendererCtrl.usableElements
            .filter(e => (isVertex ? e.type === 'RenderVertex' : e.type !== 'RenderVertex'))
            .map(e => e.name);

        // Check if the name exists in elements or namesToAvoid
        if ([...elements, ...namesToAvoid].includes(name)) {
            return { valid: false, message: `Tên ${name} đã được dùng` };
        }

        // Execute optional onValidate callback or return valid result
        return onValidate?.(idx, name, type) || { valid: true };
    }

    // Return error if the name does not match the regex pattern
    return { valid: false, message: `Tên ${name} không đúng` };
}

/**
 * Map of HTTP status codes to error messages in Vietnamese
 */
const httpCodeErrorMap = {
    403: 'Không có quyền cập nhật đối tượng',
    404: 'Không tìm thấy đối tượng',
    502: 'Lỗi kết nối',
    500: 'Lỗi hệ thống',
};

/**
 * Executes a construction callback and handles HTTP errors with appropriate error messages.
 *
 * @param {() => Promise<ApplyConstructionResponse>} cb - The construction callback to execute
 * @returns {Promise<ApplyConstructionResponse>} The response from the construction operation
 * @throws {NonCriticalDocumentAPIErr} For 403, 404, or 500 status codes
 * @throws {CriticalDocumentAPIErr} For 502 status code
 * @throws {Error} For any other errors that occur during execution
 */
export async function constructExec(cb: () => Promise<ApplyConstructionResponse>): Promise<ApplyConstructionResponse> {
    try {
        return await cb();
    } catch (e) {
        if (e.name === AxiosError.name) {
            const status = e.response.status;
            const message = httpCodeErrorMap[status] || 'Lỗi không xác định';
            if ([403, 404, 500].includes(status)) throw new NonCriticalDocumentAPIErr(message, e);
            else if (status === 502) throw new CriticalDocumentAPIErr(message, e);
        }

        throw e;
    }
}

/**
 * Abstract base class for all geometry tools in the editor.
 * Provides common functionality for handling mouse and keyboard events,
 * managing tool states, and interacting with geometric elements.
 *
 * @template TState - Type of tool state, must extend ToolState
 */
export abstract class GeometryTool<TState extends ToolState> implements Tool {
    /** Identifies this as a Tool type for the input handler system */
    readonly type: UserInputHandlerType = 'Tool';
    /** Array of mouse handling items for this tool */
    readonly mouseHandling: MouseHandlingItem[] = [];
    /** Array of keyboard handling items for this tool */
    readonly keyboardHandling: KeyboardHandlingItem[] = [];
    readonly pointerHandling: PointerHandlingItem[] = [];

    protected lastPointerMove: GeoPointerEvent;
    protected requestedFrame: boolean;
    /** Flag indicating whether the tool has been started */
    protected started = false;

    /** The last hit context that was highlighted by this tool */
    protected lastHitCtx: GeoSelectHitContext;

    /** Custom cursor to display when this tool is active */
    protected toolCursor?: Cursor[] = undefined;

    // The caching reflow sync, use to cache hit element and handle mouse move event
    protected pointerMoveCachingReflowSync = new CachingReflowSync<GeoPointerEvent, any>();

    /**
     * Function that filters which elements this tool can select
     * Used as parameters for the constructor when building elements
     */
    protected readonly filterElementFunc?: (el: GeoRenderElement) => boolean;

    protected selLogic?: MultiElementSelector<SelectableType[]> | ElementSelector<SelectableType>;

    /**
     * Creates a new instance of GeometryTool.
     *
     * @param {GeometryEditor} editor - The geometry editor instance this tool belongs to
     * @param {GeometryToolBar} toolbar - The toolbar this tool is part of
     */
    constructor(
        public readonly editor: GeometryEditor,
        readonly toolbar: GeometryToolBar
    ) {
        this.registerKeyboardHandling(
            new (class extends KeyboardHandlingItem {
                override global = false;
            })(['esc'])
        );
    }

    /**
     * Called when this tool is attached to a viewport.
     * Override this method to perform initialization when the tool is attached.
     */
    onAttachViewport() {}

    /**
     * Called when this tool is detached from a viewport.
     * Override this method to perform cleanup when the tool is detached.
     */
    onDetachViewport() {}

    /** Optional child toolbar associated with this tool */
    childToolbar?: ToolBar<any, Tool>;

    /**
     * Registers one or more mouse handling items with this tool.
     *
     * @param {MouseHandlingItem[]} handling - The mouse handling items to register
     */
    registerMouseHandling(...handling: MouseHandlingItem[]) {
        this.mouseHandling.push(...handling);
    }

    /**
     * Registers one or more keyboard handling items with this tool.
     *
     * @param {KeyboardHandlingItem[]} handling - The keyboard handling items to register
     */
    registerKeyboardHandling(...handling: KeyboardHandlingItem[]) {
        this.keyboardHandling.push(...handling);
    }

    doRegisterPointer() {
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    registerPointerHandling(...handling: PointerHandlingItem[]) {
        this.pointerHandling.push(...handling);
    }

    /**
     * Resets the state of this tool to its initial state.
     * Clears selections, ends preview mode, removes highlights, and resets flags.
     * Also applies or resets the element filter function.
     */
    resetState() {
        const docCtrl = getFocusDocCtrl(this.editor, this.toolbar.viewport.id);
        if (docCtrl) {
            this.editor.clearSelectedElInDoc(docCtrl);
            syncEndPreviewModeCommand(docCtrl);
        }
        this.editor.removeHighlight(this.lastHitCtx);
        this.lastHitCtx = undefined;
        this.requestedFrame = false;
        this.started = false;
        if (this.filterElementFunc) this.editor.filterElementFunc = this.filterElementFunc;
        else this.editor.resetFilterElementFunc();
    }

    /**
     * Gets the type of this geometry tool.
     * Must be implemented by derived classes.
     *
     * @returns {GeometryToolType} The type of this geometry tool
     */
    abstract get toolType(): GeometryToolType;

    /**
     * Gets the current state of this tool from the toolbar.
     *
     * @returns {TState} The current state of this tool
     */
    get toolState(): TState {
        return this.toolbar.toolState(this.toolType);
    }

    /**
     * Mouse event handler for this tool.
     * Manages cursor state and delegates mouse events to the tool's handleMouseEvent method.
     */
    readonly mouseHandler = new (class implements MouseEventListener<NativeEventTarget<any>> {
        /** Subject that emits cursor changes */
        cursor = new BehaviorSubject<Cursor[] | undefined>(undefined);

        /**
         * Creates a new mouse handler for the specified tool.
         *
         * @param {GeometryTool<ToolState>} tool - The tool this handler belongs to
         */
        constructor(public tool: GeometryTool<ToolState>) {}

        onEvent(event: MouseEventData<any>): MouseEventData<any> {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            return handleIfPointerNotInError(this.tool, this.tool.handleMouseEvent, event);
        }
    })(this);

    readonly pointerHandler = new (class implements PointerEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject<Cursor[] | undefined>(undefined);

        constructor(public tool: GeometryTool<ToolState>) {}

        onEvent(event: PointerEventData<NativeEventTarget<any>>): PointerEventData<NativeEventTarget<any>> {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            if ('nativeEvent' in event)
                // handle UI event
                return handleIfPointerNotInError(this.tool, this.tool.handlePointerEvent, event);
            else return this.tool.handleNonUIPointerEvent(event);
        }
    })(this);

    /**
     * Keyboard event handler for this tool.
     * Delegates keyboard events to the tool's handleKeyboardEvent method.
     */
    readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        /**
         * Creates a new keyboard handler for the specified tool.
         *
         * @param {GeometryTool<ToolState>} tool - The tool this handler belongs to
         */
        constructor(public tool: GeometryTool<ToolState>) {}

        /**
         * Handles keyboard events for the tool.
         * Checks if the tool is enabled before processing the event.
         *
         * @param {GeoKeyboardEvent} event - The keyboard event to handle
         * @returns {GeoKeyboardEvent} The processed event
         */
        onEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            return handleIfPointerNotInError(this.tool, this.tool.handleKeyboardEvent, event);
        }
    })(this);

    /**
     * Handles keyboard events for this tool.
     * Each tool has a slection logic, if there is something selected, this tool will cancel the selection
     * and allow the user to continue. If not, the toolbar will handle.
     *
     * @param {GeoKeyboardEvent} event - The keyboard event to handle
     * @returns {GeoKeyboardEvent} The processed event
     */
    handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        const docCtrl = getFocusDocCtrl(this.editor, this.toolbar.viewport.id);

        if (
            event.nativeEvent.key === 'Escape' &&
            docCtrl &&
            this.selLogic?.multiple &&
            (this.selLogic as MultiElementSelector<any>).isPartial
        ) {
            this.resetState();
            event.continue = false;
        }

        return event;
    }

    /**
     * Called when this tool loses focus.
     * Resets the cursor, ends preview mode, and resets the tool state.
     */
    onBlur() {
        if (this.pointerHandler.cursor.value) this.pointerHandler.cursor.next(undefined);
        this.resetState();
    }

    /**
     * Called when this tool gains focus.
     * Sets the tool cursor, resets the tool state, and applies the element filter function.
     */
    onFocus() {
        if (this.toolCursor) this.pointerHandler.cursor.next(this.toolCursor);
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        this.resetState();
        if (this.filterElementFunc) this.editor.filterElementFunc = this.filterElementFunc;
    }

    /**
     * Handles tool-specific events.
     * Currently only handles 'change' events by delegating to processChangeToolEvent.
     *
     * @param {GeoToolEventData} event - The tool event to handle
     * @returns {GeoToolEventData} The processed event
     */
    handleToolEvent(event: GeoToolEventData): GeoToolEventData {
        switch (event.eventType) {
            case 'change': {
                this.processChangeToolEvent(event);
                break;
            }
            default:
                break;
        }

        return event;
    }

    /**
     * Processes 'change' tool events.
     * Can be overridden by derived classes to handle tool-specific change events.
     *
     * @param {GeoToolEventData} event - The change event to process
     * @returns {Promise<GeoToolEventData>} A promise that resolves to the processed event
     * @protected
     */
    protected async processChangeToolEvent(event: GeoToolEventData): Promise<GeoToolEventData> {
        return event;
    }

    /**
     * Determines if this tool can be focused in the specified viewport.
     * A tool can be focused if it's not disabled and exactly one document is focused.
     *
     * @param {string} vpId - The ID of the viewport
     * @returns {boolean} True if the tool can be focused, false otherwise
     */
    focusAble(vpId: string): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;

        return this.editor.selectDelegator.getFocusedDocs(vpId)?.length === 1 && true;
    }

    handleMouseEvent(event: MouseEventData<any>): MouseEventData<any> {
        return event;
    }

    handlePointerEvent(event: PointerEventData<any>): PointerEventData<any> {
        return event;
    }

    handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    /**
     * Checks whether the current mouse event should be handled as a click.
     * A click should be handled if:
     * 1. The tool has already started, or
     * 2. There is a valid document controller and the mouse position is valid for that controller
     *
     * @param {GeoMouseEvent} event - The mouse event to check
     * @returns {boolean} True if the event should be handled as a click, false otherwise
     */
    shouldHandleClick(event: GeoPointerEvent): boolean {
        // Get the selecting document control and the mouse position
        const docCtrl = getFocusDocCtrl(this.editor, event.viewport.id);
        const mousePos = mouseLocation(event);

        // If the tool has already started, handle the click event
        if (this.started) return true;

        // Check if we have a valid selecting document control
        if (
            !docCtrl ||
            !docCtrl.state.globalId ||
            // If the mouse position is not valid for the selecting document control, return false
            !validatePointerPos(mousePos, docCtrl)
        ) {
            return false;
        }
        // If the event is valid, return true
        return true;
    }

    /**
     * Gets a geometry document controller by its local ID in the specified viewport.
     *
     * @param {ViewportManager} vm - The viewport manager
     * @param {number} localId - The local ID of the document
     * @returns {GeoDocCtrl} The geometry document controller, or undefined if not found
     */
    getGeoDoc(vm: ViewportManager, localId: number): GeoDocCtrl {
        return this.editor.regMan.registry<GeoDocCtrl>(geoDocReg(vm.id))?.getEntity(localId);
    }

    /**
     * Called when this tool is disabled.
     * Override this method to perform cleanup when the tool is disabled.
     */
    onDisable() {}

    /**
     * Called when this tool is enabled.
     * Override this method to perform initialization when the tool is enabled.
     */
    onEnable() {}

    /**
     * Gets the document controller, position, and hit context for a mouse event.
     * Handles highlighting of hit elements and cursor changes.
     * Throws GeoPointerNotInError if the mouse is not in a valid position.
     *
     * @param {GeoMouseEvent} event - The mouse event
     * @param {boolean} [stopSnap=false] - Whether to disable snapping for this operation
     * @returns {Object} An object containing:
     *   - ctrl: The document controller
     *   - pos: The position in geometry coordinates
     *   - hitCtx: The hit context if an element was hit
     *   - hitEl: The hit element if any
     *   - docGlobalId: The global ID of the document
     *   - renderer: The renderer controller
     * @throws {GeoPointerNotInError} If the mouse is not in a valid position
     */
    posAndCtrl(
        event: LocatableEvent<any>,
        stopSnap = false
    ): {
        ctrl: GeoDocCtrl;
        pos: Position;
        hitCtx: GeoSelectHitContext;
        hitEl: GeoRenderElement;
        docGlobalId: DocumentId;
        renderer: GeoRenderer;
    } {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        const renderer = ctrl.rendererCtrl;
        const pointerPos = mouseLocation(event);
        if (!validatePointerPos(pointerPos, ctrl)) {
            if (this.lastHitCtx) {
                this.editor.removeHighlight(this.lastHitCtx);
                this.lastHitCtx = undefined;
            }
            throw new GeoPointerNotInError();
        }

        const pointerPosInLayer = calculatePosInLayer(pointerPos, ctrl);

        // Calculate the position in the layer and convert it to the geo position based on snap settings
        const pointerPosInGeo = convertPointBySnapTool(ctrl, renderer.layerToGeoPos(pointerPosInLayer), stopSnap);
        const hitCtx = this.editor.checkHitInternal(ctrl.layers[0], event);

        // remove highlight if the hit ctx is not the same as previous hit ctx
        if (this.lastHitCtx?.hitDetails?.el && this.lastHitCtx.hitDetails.el !== hitCtx?.hitDetails?.el) {
            this.editor.removeHighlight(this.lastHitCtx);
            this.lastHitCtx = undefined;
            if (this.pointerHandler.cursor.value) this.pointerHandler.cursor.next(this.toolCursor || undefined);
        }

        if (hitCtx?.hitDetails?.el && this.lastHitCtx?.hitDetails?.el !== hitCtx?.hitDetails?.el) {
            // record last hit if some element is hit
            this.editor.highlight(hitCtx);
            this.lastHitCtx = hitCtx;
            if (!this.pointerHandler.cursor.value || this.pointerHandler.cursor.value === this.toolCursor)
                this.pointerHandler.cursor.next(pointerCursor);
            event.continue = false;
            event.nativeEvent.preventDefault();
        }

        return {
            ctrl: ctrl,
            pos: pointerPosInGeo,
            hitCtx: hitCtx,
            hitEl: hitCtx?.hitDetails?.el,
            docGlobalId: ctrl.state.globalId,
            renderer: renderer,
        };
    }

    isPreselectionHit(event: LocatableEvent<any>): boolean {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        const pointerPos = mouseLocation(event);
        if (!validatePointerPos(pointerPos, ctrl)) {
            throw new GeoPointerNotInError();
        }

        const hitCtx = this.editor.checkHitInternal(ctrl.layers[0], event, true);

        return !!hitCtx && !!hitCtx.hitDetails;
    }
}
