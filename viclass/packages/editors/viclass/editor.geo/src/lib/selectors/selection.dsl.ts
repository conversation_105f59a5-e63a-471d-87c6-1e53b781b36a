/**
 * Implement a dsl to describe the selection logic for tools. Example:
 *
 * select(RenderVertex, { realEl: true, autoAccept: true }) is a selector logic that will try to select a real render vertex
 *
 * or(select(RenderVertex, { preview: true }), select(RenderLine, { realEl : true })) is a selector logic that will try to select
 * a preview render vertex or a real render line.
 *
 * and(select(RenderVertex, { preview: true }), select(RenderLine, { realEl : true })) is a selector logic that selects both
 * preview render vertex and real render line. Only complete when both are selected
 *
 * then(s1, s2) is a selector logic that select s1 first, when s1 is complete, it will start selecting s2.
 *
 */
import { LocatableEvent } from '@viclass/editor.core';
import { GeoDocCtrl } from '../objects';
import { ElementSelector, SelectableType, SelectorOptions } from './selectors';

/**
 * Recursively flatten the selection to make it easier to handle
 * @param selection
 * @param result store the result
 * @param level true - flatten the whole thing, number - flatten maximum a number of time
 */
export function flatten<T extends SelectableType[]>(selection: SelectableType[], result: T, level: number | boolean) {
    if (typeof level == 'number' && level < 0) result.push(selection);
    else
        for (const s of selection) {
            if (Array.isArray(s)) {
                flatten(s, result, typeof level == 'number' ? level - 1 : level);
            } else {
                result.push(s);
            }
        }
}

export type MultiSelectorOptions<OutType extends SelectableType[]> = Pick<
    SelectorOptions<OutType>,
    'onComplete' | 'onReset' | 'name'
> & {
    // callback to inform about new selection within a multi selector
    // if the callback return false, selection will be completed
    // if the callback return true, the internal selector will be
    // reset to select a new element of the same type
    onPartialSelection?: (
        newSel: OutType[number],
        curSel: OutType,
        selector: ElementSelector<OutType>,
        doc: GeoDocCtrl
    ) => boolean;

    flatten?: boolean | number; // passing in number to limit the depth of flattening
};

/**
 * Multi Element Selector compose individual selectors or other multi selectors (input selectors)
 * to perform complex selection interaction.
 *
 * For example, repeatedly select vertexes
 */
export abstract class MultiElementSelector<
    T extends SelectableType[],
    OT extends MultiSelectorOptions<T> = MultiSelectorOptions<T>,
> extends ElementSelector<T, OT> {
    override readonly multiple: boolean = true;
    abstract selectors: ElementSelector<SelectableType>[];
    oSelected: T; // the original selected elements before its own flattening

    readonly isPartial: boolean;

    public override curGeneratedId: number = 0;

    private _isAccepted: boolean = false;

    // multi selector doesn't have id
    resetId() {}

    override finalizeAccept(doc: GeoDocCtrl): void {
        for (const s of this.selectors) {
            s.finalizeAccept(doc);
        }
        if (this.acceptMarked) {
            const selected: T = this.markedSelect;

            if (!this.options?.flatten) this.selected = selected;
            else {
                this.selected = [] as T;
                this.oSelected = this.markedSelect;
                flatten<T>(selected, this.selected, this.options.flatten);
            }

            this.isAccepted = true;
            if (this.options?.onComplete) {
                this.options.onComplete(this, doc);
            }

            this.acceptMarked = false;
            this.markedSelect = undefined;
        }
    }

    override clearTrial(doc: GeoDocCtrl) {
        for (const s of this.selectors) s.clearTrial(doc);
    }

    override get isAccepted(): boolean {
        return this._isAccepted;
    }

    protected override set isAccepted(value: boolean) {
        this._isAccepted = value;
    }

    get(name: string): ElementSelector<SelectableType> | undefined {
        for (const s of this.selectors) {
            if (s.getOption('name') === name) {
                return s;
            }

            if (s instanceof MultiElementSelector) {
                const found = s.get(name);
                if (found) return found;
            }
        }

        return undefined;
    }

    /**
     * Finds all descendant selectors that have a name included in the specified list of names.
     * This method performs a depth-first search through the selector hierarchy.
     *
     * @param names - An array of names to search for.
     * @returns An array of matching `ElementSelector` instances.
     */
    getArr(names: string[], result?: ElementSelector<SelectableType>[]): ElementSelector<SelectableType>[] {
        if (!result) result = [];
        for (const s of this.selectors) {
            if (names.includes(s.getOption('name'))) result.push(s);
            if (s instanceof MultiElementSelector) s.getArr(names, result);
        }
        return result;
    }
}

export function and<OutType extends SelectableType[]>(
    selectors: ElementSelector<SelectableType>[],
    options: MultiSelectorOptions<OutType>
): MultiElementSelector<OutType> {
    throw new Error('Not implemented');
}

export type OrSelectorOptions = Pick<
    MultiSelectorOptions<SelectableType[]>,
    'onComplete' | 'flatten' | 'onReset' | 'name'
>;

export function or<T extends SelectableType[] = SelectableType[]>(
    selectors: ElementSelector<SelectableType>[],
    options?: OrSelectorOptions
): OrSelector<T> {
    return new OrSelector<T>(selectors, options);
}

// export function oneOf(selectors: InputSelector[], options?: MultiSelectorOptions): MultiElementSelector {
//     throw new Error('Not implemented');
// }

export class OrSelector<T extends SelectableType[]> extends MultiElementSelector<T> {
    declare selected?: T;
    declare isPartial: boolean;

    private lastHit = -1;
    private lastPreview = -1;
    private curPartialBranch = -1;

    constructor(
        public selectors: ElementSelector<SelectableType>[],
        protected override options: OrSelectorOptions
    ) {
        super();
        this.options = options;

        for (const selector of selectors) {
            const onComplete = selector.getOption('onComplete');
            selector.setOption('onComplete', (selector, doc) => {
                this.onInternalComplete(selector, doc, onComplete);
            });
        }
    }

    private onInternalComplete(
        selector: ElementSelector<SelectableType>,
        doc: GeoDocCtrl,
        onCompleteOfInternal: ((internalSelector: ElementSelector<SelectableType>, doc: GeoDocCtrl) => void) | undefined
    ) {
        // call the internal onComplete because user might wanted to listen to
        // the internal completion
        this.selected = [] as T;
        this.oSelected = [selector.selected] as T;
        if (this.options?.flatten) {
            flatten([selector.selected], this.selected, this.options.flatten);
        }
        if (onCompleteOfInternal) onCompleteOfInternal(selector, doc);
        this.accept([selector.selected] as T, doc);
    }

    override tryHit = this.tryHitOrPreview.bind(this, true);
    override tryPreview = this.tryHitOrPreview.bind(this, false);

    tryHitOrPreview(tryingHit: boolean, event: LocatableEvent<any>, doc: GeoDocCtrl): SelectableType[] | undefined {
        if (!tryingHit && this.lastHit >= 0) return undefined; // if trying preview, but something already hit, don't continue

        if (this.curPartialBranch >= 0) {
            const result = this.doHitOrPreview(tryingHit, event, doc, this.curPartialBranch);

            if (result) return result;
        } else {
            for (let i = 0; i < this.selectors.length; i++) {
                const result = this.doHitOrPreview(tryingHit, event, doc, i);
                if (result) return result;
            }
        }

        if (tryingHit) {
            if (this.lastHit >= 0) this.selectors[this.lastHit].clearTrial(doc);
            this.lastHit = -1; // if after trying all hit, nothing is hit then lastHit is reset
        } else {
            if (this.lastPreview >= 0) this.selectors[this.lastPreview].clearTrial(doc);
            this.lastPreview = -1;
        }

        return undefined;
    }

    private doHitOrPreview(
        tryingHit: boolean,
        event: LocatableEvent<any>,
        doc: GeoDocCtrl,
        sIdx: number
    ): SelectableType[] | undefined {
        const s = this.selectors[sIdx];

        const internalSelect = tryingHit ? s.tryHit(event, doc) : s.tryPreview(event, doc);

        if (s.multiple && (s as MultiElementSelector<any>).isPartial) {
            this.isPartial = true;
            this.curPartialBranch = sIdx; // record the current partial branch so next time we can check that only
        }

        if (internalSelect) {
            // record the last hit / last preview and clear previous trial
            if (tryingHit) {
                // clear the last match selector, because now, another selector is producing selection matching
                if (this.lastHit >= 0 && sIdx != this.lastHit) this.selectors[this.lastHit].clearTrial(doc);
                if (this.lastPreview >= 0 && this.lastPreview != sIdx) this.selectors[this.lastPreview].clearTrial(doc); // do not clear trial of the current hit
                this.lastPreview = -1;
                this.lastHit = sIdx;
            } else {
                if (this.lastHit >= 0 && this.lastHit != sIdx) this.selectors[this.lastHit].clearTrial(doc); // do not clear trial of the sIdx
                if (this.lastPreview >= 0 && sIdx != this.lastPreview) this.selectors[this.lastPreview].clearTrial(doc);
                this.lastPreview = sIdx;
                this.lastHit = -1;
            }

            if (this.options?.flatten) {
                const flattenResult = [] as SelectableType[];
                flatten([internalSelect], flattenResult, 1000);
                return flattenResult;
            } else {
                return [internalSelect];
            }
        }

        return undefined;
    }

    override reset(keepPreview: boolean = false): void {
        this.selected = undefined;
        this.oSelected = undefined;
        this.isAccepted = false;
        this.isPartial = false;
        this.lastHit = -1;
        this.curPartialBranch = -1;
        for (const s of this.selectors) s.reset(keepPreview);

        if (this.options?.onReset) this.options.onReset(this);
    }
}

export function then(
    selectors: ElementSelector<SelectableType>[],
    options: MultiSelectorOptions<SelectableType[]>
): ThenSelector {
    return new ThenSelector(selectors, options);
}

export class ThenSelector extends MultiElementSelector<SelectableType[]> {
    declare selected?: SelectableType[];
    declare isPartial: boolean;
    private curSelector = 0;

    constructor(
        public selectors: ElementSelector<SelectableType>[],
        protected override options: MultiSelectorOptions<SelectableType[]>
    ) {
        super();
        this.options = options;

        for (const selector of selectors) {
            const onComplete = selector.getOption('onComplete');
            selector.setOption('onComplete', (selector, doc) => {
                this.onInternalComplete(selector, doc, onComplete);
            });
        }
    }

    private onInternalComplete(
        selector: ElementSelector<SelectableType>,
        doc: GeoDocCtrl,
        onCompleteOfInternal: ((internalSelector: ElementSelector<SelectableType>, doc: GeoDocCtrl) => void) | undefined
    ) {
        if (!this.selected) {
            this.selected = [];
            this.oSelected = [];
        }
        // push the result of the internal selection into the selected array
        // before doing anything to ensure correct state.
        this.oSelected.push(selector.selected);
        if (this.options?.flatten) {
            const newResult = [];
            flatten([selector.selected], newResult, this.options?.flatten);
            this.selected.push(...newResult);
        } else this.selected.push(selector.selected);

        // call the internal onComplete because user might wanted to listen to
        // the internal completion
        if (onCompleteOfInternal) onCompleteOfInternal(selector, doc);
        this.isPartial = true;
        let continued = false;

        if (this.options?.onPartialSelection)
            continued = this.options.onPartialSelection(selector.selected, this.selected, this, doc);
        else continued = true;

        if (this.curSelector == this.selectors.length - 1) continued = false;

        this.selectors[this.curSelector].reset(true);
        this.curSelector++;

        if (!continued) {
            this.accept(this.selected, doc);
        }
    }

    override tryHit = this.tryHitOrPreview.bind(this, true);
    override tryPreview = this.tryHitOrPreview.bind(this, false);

    tryHitOrPreview(hit: boolean, event: LocatableEvent<any>, doc: GeoDocCtrl): SelectableType[] | undefined {
        if (this.acceptMarked || this.curSelector >= this.selectors.length) return undefined;

        const s = this.selectors[this.curSelector];

        const curSelected = this.selected ? [...this.selected] : [];
        const internalSelect = this.acceptMarked ? undefined : hit ? s.tryHit(event, doc) : s.tryPreview(event, doc);

        if (s.multiple && (s as MultiElementSelector<any>).isPartial) {
            this.isPartial = true;
        }

        if (this.acceptMarked) return undefined;

        if (internalSelect) {
            // if can select something new

            if (this.options?.flatten) {
                const flattenResult = [] as SelectableType[];
                flatten([internalSelect], flattenResult, this.options.flatten);
                return [...curSelected, ...flattenResult];
            } else {
                return [...curSelected, internalSelect];
            }
        }

        return undefined;
    }

    override reset(keepPreview: boolean = false): void {
        this.oSelected = undefined;
        this.curGeneratedId = 0;
        this.selected = undefined;
        this.isAccepted = false;
        this.isPartial = false;
        this.curSelector = 0;
        for (const s of this.selectors) s.reset(keepPreview);

        if (this.options?.onReset) this.options.onReset(this);
    }
}

export type RepeatSelectorOptions<T extends SelectableType[]> = MultiSelectorOptions<T> & {
    count?: number;
};

export function repeat<T extends SelectableType>(
    selector: ElementSelector<T>,
    options: RepeatSelectorOptions<T[]>
): RepeatSelector<T> {
    return new RepeatSelector<T>([selector], options);
}

export class RepeatSelector<T extends SelectableType> extends MultiElementSelector<T[], MultiSelectorOptions<T[]>> {
    declare selected?: T[];
    declare isPartial: boolean;

    constructor(
        public selectors: ElementSelector<T>[],
        protected override options: RepeatSelectorOptions<T[]>
    ) {
        super();
        this.options = options;

        if (!this.options.onPartialSelection && !this.options.count) {
            throw new Error(
                'Partial selection check and count cannot be both undefined. There must be a condition to end repeat.'
            );
        }

        const onComplete = this.selectors[0].getOption('onComplete');
        this.selectors[0].setOption('onComplete', (selector, doc) => {
            this.onInternalComplete(selector, doc, onComplete);
        });
    }

    private onInternalComplete(
        selector: ElementSelector<T>,
        doc: GeoDocCtrl,
        onCompleteOfInternal: ((internalSelector: ElementSelector<T>, doc: GeoDocCtrl) => void) | undefined
    ) {
        if (!this.selected) {
            this.selected = [];
            this.oSelected = [];
        }
        // push the result of the internal selection into the selected array
        // before doing anything to ensure correct state.
        this.oSelected.push(selector.selected);
        if (this.options?.flatten) {
            const newResult = [];
            flatten([selector.selected], newResult, this.options?.flatten);
            this.selected.push(...newResult);
        } else this.selected.push(selector.selected);

        // call the internal onComplete because user might wanted to listen to
        // the internal completion
        if (onCompleteOfInternal) onCompleteOfInternal(selector, doc);

        this.isPartial = true;
        let repeat = true;
        if (this.options?.onPartialSelection)
            repeat = this.options.onPartialSelection(selector.selected, this.selected, this, doc);

        if (this.options?.count > 0 && this.selected && this.selected.length == this.options?.count) repeat = false;

        if (!repeat) this.accept(this.selected, doc);
        else this.selectors[0].reset(true); // reset the internal selector to start the selection of the next item
    }

    override tryHit = this.tryHitOrPreview.bind(this, true);
    override tryPreview = this.tryHitOrPreview.bind(this, false);

    tryHitOrPreview(hit: boolean, event: LocatableEvent<any>, doc: GeoDocCtrl): T[] | undefined {
        if (this.acceptMarked) return undefined;

        const curSelected = this.selected ? [...this.selected] : [];
        const internalSelect = this.isAccepted
            ? undefined
            : hit
              ? this.selectors[0].tryHit(event, doc)
              : this.selectors[0].tryPreview(event, doc);

        if (this.selectors[0].multiple && (this.selectors[0] as MultiElementSelector<any>).isPartial) {
            this.isPartial = true;
        }

        if (this.acceptMarked) return undefined;

        if (internalSelect) {
            if (this.options?.flatten) {
                const flattenResult = [] as T[]; // this is a bit dirty, because flattening doesn't ensure the typing
                flatten([internalSelect], flattenResult, this.options.flatten);
                return [...curSelected, ...flattenResult];
            } else {
                return [...curSelected, internalSelect];
            }
        }

        return undefined;
    }

    override reset(keepPreview: boolean = false): void {
        this.oSelected = undefined;
        this.curGeneratedId = 0;
        this.selected = undefined;
        this.isAccepted = false;
        this.isPartial = false;
        this.selectors[0].reset(keepPreview);

        if (this.options?.onReset) this.options.onReset(this);
    }
}
