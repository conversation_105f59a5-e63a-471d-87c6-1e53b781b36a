import { line, Point, point, vector } from '@flatten-js/core';
import { <PERSON>ursor } from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { GeoEpsilon, RenderVertex } from '../model';
import { PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import {
    MultiSelectorOptions,
    or,
    OrSelector,
    repeat,
    RepeatSelector,
    RepeatSelectorOptions,
    then,
    ThenSelector,
} from './selection.dsl';
import { SelectableType } from './selectors';
import { vertexOnStroke, VertexOnStroke } from './stroke.selector';
import { vertex } from './vertex.selector';

export type SelectedVertex = [RenderVertex] | VertexOnStroke;

export function vert(s: SelectedVertex | RenderVertex): RenderVertex {
    if (Array.isArray(s)) {
        if (s.length == 1) return s[0];
        else return s[1];
    } else return s;
}

/**
 * Many tool when select vertex use this
 * @param q
 * @param cursor
 */
export function vertexS(q: PreviewQueue, cursor: BehaviorSubject<Cursor[]>): OrSelector<SelectedVertex> {
    return or(
        [
            vertex({ previewQueue: q, cursor: cursor, name: 'vertex' }),
            vertexOnStroke({ previewQueue: q, cursor: cursor, name: 'vertexOnStroke', syncPreview: true }),
        ],
        {
            flatten: true, // because of this, the return type of the or operator is [RenderVertex] or VertexOnStroke
        }
    );
}

/**
 * Many shape tools such as triangle tools / quad tools has following selection flows:
 * - Select the first two points, either new points (free / constrained on a shape), or existing points
 * - Select the last x points
 * - Add selected points to a list of exclusion
 *
 * Example:
 * - n = 2: regular polygon, constrained triangles, quads such as rectangle, square, etc...
 * - n = 3: Use for trapezoid
 * - n unlimited : polygon
 *
 *
 * @param q
 * @param cursor
 */
export function nPoints(
    q: PreviewQueue,
    cursor: BehaviorSubject<Cursor[]>,
    options?: Partial<RepeatSelectorOptions<SelectedVertex[]>>
): RepeatSelector<SelectedVertex> {
    const exclusion: RenderVertex[] = []; // we use closure to keep this array

    const point = vertexS(q, cursor);
    point.get('vertex').setOption(
        'refinedFilter',
        (el: RenderVertex) => exclusion.findIndex(excluded => excluded.relIndex == el.relIndex) == -1 // do not reselect anything has been selected
    );

    if (!options) options = {};

    const t = options.onPartialSelection;
    const r = options.onReset;
    options.name = options.name ?? 'repeat';
    // compose the onPartialSelection if provided, else, we use our
    options.onPartialSelection = (newSel: SelectedVertex, curSel, selector, doc) => {
        exclusion.push(vert(newSel));
        return !t || t(newSel, curSel, selector, doc);
    };
    options.onReset = selector => {
        exclusion.splice(0);
        r?.(selector);
    };

    const rep = repeat<SelectedVertex>(point, options);

    return rep;
}

/**
 * Selection logic for select triangle points with last point being projected / selected
 * with constraints.
 *
 * This is use in multiple selector from triangle to quads. Some quad construction is basicall
 * construct a triangle and the last point is inferred
 */
export function triangleWithProj(
    pQ: PreviewQueue,
    cursor: BehaviorSubject<Cursor[]>,
    projFunc: (f2p: SelectedVertex[], el: RenderVertex) => RenderVertex,
    checkFunc: (f2p: SelectedVertex[], el: RenderVertex) => boolean,
    thenOptions?: MultiSelectorOptions<SelectableType[]>
): ThenSelector {
    const f2pSelector = nPoints(pQ, cursor, { count: 2 }); // this has the form [SelectedVertex, SelectedVertex]
    const lastVertex = vertex({
        name: 'lastVertex',
        // then select a vertex
        previewQueue: pQ,
        cursor: cursor,
    });

    const selLogic = then([f2pSelector, lastVertex], thenOptions); // this has the form of [[SelectedVertex, SelectedVertex], RenderVertex]

    lastVertex.setOption('refinedFilter', el => {
        if (!selLogic.oSelected) return true;

        // filter out first 2 points
        return !(selLogic.oSelected[0] as SelectedVertex[]).map(v => vert(v).relIndex).includes(el.relIndex);
    });

    lastVertex
        .setOption('tfunc', (previewEl: RenderVertex, doc: GeoDocCtrl) => {
            const f2p = selLogic.oSelected[0] as SelectedVertex[]; // use original selected because user might set flatten option for this then selector
            return projFunc(f2p, previewEl);
        })
        .setOption('cfunc', (previewEl: RenderVertex, doc: GeoDocCtrl) => {
            const f2p = selLogic.oSelected[0] as SelectedVertex[];
            return checkFunc(f2p, previewEl);
        });

    return selLogic;
}

export function pointsFromSelected(verts: (SelectedVertex | RenderVertex)[]): Point[] {
    return verts.map(selectedVertex => {
        const v = vert(selectedVertex);
        return point(v.coords[0], v.coords[1]);
    });
}

/**
 * Transform a point by project it on the lines of the going through selected
 * vertices, and perpendicular to the line connect these two points
 * Use to transform the last point of the right triangle selection logic
 * @param f2p
 * @param el
 */
export function perpLinesTransform(f2p: SelectedVertex[], el: RenderVertex): RenderVertex {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);

    const l1 = line(fp1, vector(fp1, fp2));
    const l2 = line(fp2, vector(fp1, fp2));
    const prj1 = p.projectionOn(l1);
    const prj2 = p.projectionOn(l2);
    const d1 = p.distanceTo(prj1)[0];
    const d2 = p.distanceTo(prj2)[0];
    // select the closer projection
    const chosen = d1 < d2 ? prj1 : prj2;

    el.coords[0] = chosen.x;
    el.coords[1] = chosen.y;

    return el;
}

/**
 * Use in check last point for right angle selection logic
 * @param f2p
 * @param el
 * @returns
 */
export function perpLinesCheck(f2p: SelectedVertex[], el: RenderVertex): boolean {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);

    const l1 = line(fp1, vector(fp1, fp2));
    const l2 = line(fp2, vector(fp1, fp2));
    const prj1 = p.projectionOn(l1);
    const prj2 = p.projectionOn(l2);
    const d1 = p.distanceTo(prj1)[0];
    const d2 = p.distanceTo(prj2)[0];
    const chosen = d1 < d2 ? prj1 : prj2;

    const diff = chosen.distanceTo(p)[0];

    return diff < GeoEpsilon;
}

/**
 * Transform a point by project it on the half circle with diameter of the two selected vertices
 * Use in transform for right triangle selection logic
 * @param f2p
 * @param el
 */
export function halfCircleTransform(f2p: SelectedVertex[], el: RenderVertex): RenderVertex {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);

    const midpoint = fp1.translate(vector(fp1, fp2).multiply(0.5));
    const radius = fp1.distanceTo(fp2)[0] / 2;
    const vmid = vector(midpoint, p).normalize().multiply(radius);

    const chosen = midpoint.translate(vmid);

    el.coords[0] = chosen.x;
    el.coords[1] = chosen.y;
    return el;
}

/**
 * Check if the element is on the half circle created by the 2 selected vertex
 * Use in check for right triangle selection logic
 * @param f2p
 * @param el
 * @returns
 */
export function halfCircleCheck(f2p: SelectedVertex[], el: RenderVertex): boolean {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);
    const midpoint = fp1.translate(vector(fp1, fp2).multiply(0.5));
    const radius = fp1.distanceTo(fp2)[0] / 2;
    const diff = Math.abs(p.distanceTo(midpoint)[0] - radius);

    return diff < GeoEpsilon;
}

export function perpBisectorTransform(f2p: SelectedVertex[], el: RenderVertex): RenderVertex {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);

    const midpoint = fp1.translate(vector(fp1, fp2).multiply(0.5));
    const l1 = line(midpoint, vector(fp1, fp2).normalize());

    const chosen = p.projectionOn(l1);

    el.coords[0] = chosen.x;
    el.coords[1] = chosen.y;
    return el;
}

export function circleTransform(f2p: SelectedVertex[], el: RenderVertex, cIdx?: number): RenderVertex {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);
    // first two points are hypothenuse
    const radius = fp1.distanceTo(fp2)[0];

    const c1 = fp1.translate(vector(fp1, p).normalize().multiply(radius));
    const c2 = fp2.translate(vector(fp2, p).normalize().multiply(radius));

    const d1 = c1.distanceTo(p)[0];
    const d2 = c2.distanceTo(p)[0];

    const chosen = cIdx != undefined ? (cIdx == 0 ? c1 : c2) : d1 < d2 ? c1 : c2;

    el.coords[0] = chosen.x;
    el.coords[1] = chosen.y;

    return el;
}

export function perpBisectorCheck(f2p: SelectedVertex[], el: RenderVertex): boolean {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);

    const diff = Math.abs(fp1.distanceTo(p)[0] - fp2.distanceTo(p)[0]);

    return diff < GeoEpsilon;
}

export function circleCheck(f2p: SelectedVertex[], el: RenderVertex): boolean {
    const [fp1, fp2, p] = pointsFromSelected([...f2p, el]);
    // first two points are hypothenuse
    const radius = fp1.distanceTo(fp2)[0];
    const d1 = p.distanceTo(fp1)[0];
    const d2 = p.distanceTo(fp2)[0];
    const center = d2 < d1 ? (d1 <= radius ? fp1 : fp2) : d2 <= radius ? fp2 : fp1;
    const diff = Math.abs(p.distanceTo(center)[0] - radius);

    return diff < GeoEpsilon;
}
